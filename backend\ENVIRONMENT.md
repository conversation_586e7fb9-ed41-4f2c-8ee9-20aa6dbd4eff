# Backend Environment Configuration Guide

This document explains how to configure environment variables for the CoBuilder backend application.

## Environment Files

### `.env` (Production/Default)
Contains the actual configuration values. **Never commit this file to version control.**

### `.env.example`
Template file showing the structure and required environment variables.

## Environment Variables

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `PORT` | Server port number | `3000` | No (defaults to 3000) |
| `MONGO_URI` | MongoDB connection string | `mongodb://localhost:27017/mydb` | Yes |
| `JWT_SECRET` | Secret key for JWT tokens | `your_secret_key` | Yes |
| `REDIS_HOST` | Redis server hostname | `localhost` | Yes |
| `REDIS_PORT` | Redis server port | `6379` | Yes |
| `REDIS_PASSWORD` | Redis server password | `your_password` | Yes |
| `GOOGLE_API_KEY` | Google AI API key | `your_api_key` | Yes |
| `FRONTEND_URL` | Production frontend URL | `https://myapp.com` | Yes |
| `LOCAL_FRONTEND_URL` | Local development frontend URL | `http://localhost:5173` | No |

## CORS Configuration

The backend automatically configures CORS using the `FRONTEND_URL` and `LOCAL_FRONTEND_URL` environment variables. This ensures that only authorized frontend applications can access the API.

### Production Setup:
```env
FRONTEND_URL=https://your-production-domain.com
LOCAL_FRONTEND_URL=http://localhost:5173
```

### Development Setup:
```env
FRONTEND_URL=http://localhost:5173
LOCAL_FRONTEND_URL=http://localhost:3000
```

## Setup Instructions

### For Local Development:
1. Copy `.env.example` to `.env`
2. Fill in your actual values
3. Start MongoDB and Redis services
4. Run: `npm start`

### For Production:
1. Set environment variables in your hosting platform
2. Ensure all required services (MongoDB, Redis) are accessible
3. Deploy the application

## Security Notes

- Never commit `.env` files to version control
- Use strong, unique values for `JWT_SECRET`
- Restrict database and Redis access to your application only
- Use HTTPS in production for `FRONTEND_URL`
- Regularly rotate API keys and secrets

## Troubleshooting

### CORS Errors:
- Check that `FRONTEND_URL` matches your frontend domain exactly
- Ensure no trailing slashes in URLs
- Verify the frontend is running on the expected port

### Database Connection Issues:
- Verify `MONGO_URI` is correct and accessible
- Check network connectivity to MongoDB server
- Ensure database user has proper permissions

### Redis Connection Issues:
- Verify Redis server is running
- Check `REDIS_HOST`, `REDIS_PORT`, and `REDIS_PASSWORD`
- Test Redis connection independently
