#!/bin/bash

# Quick Environment Update Script for Render Deployment

echo "🚀 CoBuilder Environment Update Script"
echo "======================================"

# Get backend URL from user
echo ""
echo "📝 Please enter your Render backend URL:"
echo "   (Example: https://cobuilder-backend.onrender.com)"
read -p "Backend URL: " BACKEND_URL

# Get frontend URL from user  
echo ""
echo "📝 Please enter your Render frontend URL:"
echo "   (Example: https://cobuilder-frontend.onrender.com)"
read -p "Frontend URL: " FRONTEND_URL

# Validate URLs
if [[ ! $BACKEND_URL =~ ^https?:// ]]; then
    echo "❌ Error: Backend URL must start with http:// or https://"
    exit 1
fi

if [[ ! $FRONTEND_URL =~ ^https?:// ]]; then
    echo "❌ Error: Frontend URL must start with http:// or https://"
    exit 1
fi

echo ""
echo "🔧 Updating environment files..."

# Update frontend .env
cat > frontend/.env << EOF
# Backend API Configuration
VITE_API_URL=$BACKEND_URL

# Development Configuration (uncomment for local development)
# VITE_API_URL=http://localhost:3000

# App Configuration
VITE_APP_NAME=CoBuilder
VITE_APP_VERSION=1.0.0
EOF

# Update backend .env
sed -i.bak "s|FRONTEND_URL=.*|FRONTEND_URL=$FRONTEND_URL|g" backend/.env
sed -i.bak "s|LOCAL_FRONTEND_URL=.*|LOCAL_FRONTEND_URL=http://localhost:5173|g" backend/.env

echo "✅ Updated frontend/.env"
echo "✅ Updated backend/.env"

echo ""
echo "📋 Next Steps:"
echo "1. Commit and push changes:"
echo "   git add ."
echo "   git commit -m 'Update environment URLs for Render deployment'"
echo "   git push"
echo ""
echo "2. In Render Dashboard, set these environment variables:"
echo ""
echo "   🎯 Frontend Service Environment Variables:"
echo "   VITE_API_URL=$BACKEND_URL"
echo ""
echo "   🎯 Backend Service Environment Variables:"
echo "   FRONTEND_URL=$FRONTEND_URL"
echo "   LOCAL_FRONTEND_URL=http://localhost:5173"
echo ""
echo "3. Trigger manual deploy for both services"
echo ""
echo "🔍 Debug: Check browser console for '🔧 Environment Debug Info' messages"
EOF
