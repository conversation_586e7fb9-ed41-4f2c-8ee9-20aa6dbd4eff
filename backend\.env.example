# Backend Environment Variables Template
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3000

# Database Configuration
MONGO_URI=mongodb://localhost:27017/your_database_name

# Authentication
JWT_SECRET=your_jwt_secret_key_here

# Redis Configuration (for session management and caching)
REDIS_HOST=your_redis_host
REDIS_PORT=your_redis_port
REDIS_PASSWORD=your_redis_password

# AI Service Configuration
GOOGLE_API_KEY=your_google_api_key_here

# CORS Configuration
# Frontend URLs allowed to access the backend
FRONTEND_URL=https://your-frontend-domain.com
LOCAL_FRONTEND_URL=http://localhost:5173

# Production vs Development
# For production, use your actual domain
# For development, use localhost URLs

# Example Production Configuration:
# FRONTEND_URL=https://your-app.vercel.app
# MONGO_URI=mongodb+srv://username:<EMAIL>/database

# Example Development Configuration:
# FRONTEND_URL=http://localhost:5173
# MONGO_URI=mongodb://localhost:27017/your_local_db
