# 🚨 Render Deployment Fix Guide

## Current Issue
Frontend is connecting to `localhost:3000` instead of your Render backend URL.

## 🔧 Step-by-Step Fix

### 1. **Get Your Backend URL**
1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click on your **backend service**
3. Copy the URL (should be like: `https://your-backend-name.onrender.com`)

### 2. **Update Frontend Environment Variables**

**Option A: Update .env file locally and redeploy**
```bash
# Edit frontend/.env
VITE_API_URL=https://YOUR-ACTUAL-BACKEND-URL.onrender.com
```

**Option B: Set Environment Variables in Render Dashboard**
1. Go to your **frontend service** in Render
2. Go to **Environment** tab
3. Add: `VITE_API_URL` = `https://your-backend-url.onrender.com`
4. Click **Save Changes**

### 3. **Update Backend CORS Settings**
1. Go to your **backend service** in Render
2. Go to **Environment** tab
3. Add/Update these variables:
   - `FRONTEND_URL` = `https://your-frontend-url.onrender.com`
   - `LOCAL_FRONTEND_URL` = `http://localhost:5173`

### 4. **Force Rebuild Both Services**
1. In Render dashboard, go to each service
2. Click **Manual Deploy** → **Deploy latest commit**
3. Wait for both to complete

## 🔍 Debug Steps

### Check Frontend Environment Variables
1. Open your deployed frontend in browser
2. Open Developer Tools (F12)
3. Check Console for debug messages starting with "🔧 Environment Debug Info:"
4. Verify `VITE_API_URL` shows your correct backend URL

### Check Backend CORS
1. Open your backend URL in browser: `https://your-backend.onrender.com`
2. You should see "Hello World"
3. Check backend logs in Render dashboard for CORS errors

### Test API Connection
1. In browser console on your frontend, run:
```javascript
fetch('https://your-backend.onrender.com/')
  .then(r => r.text())
  .then(console.log)
  .catch(console.error)
```

## 🚨 Common Issues

### Issue 1: Environment Variables Not Set During Build
- **Solution**: Set `VITE_API_URL` in Render frontend environment variables
- **Then**: Trigger manual deploy

### Issue 2: CORS Errors
- **Solution**: Ensure backend `FRONTEND_URL` matches your frontend domain exactly
- **Check**: No trailing slashes in URLs

### Issue 3: Backend Not Responding
- **Solution**: Check backend logs in Render dashboard
- **Check**: Backend service is running and not sleeping

### Issue 4: Cache Issues
- **Solution**: Hard refresh browser (Ctrl+Shift+R)
- **Or**: Clear browser cache and localStorage

## 📝 Quick Fix Commands

```bash
# 1. Update frontend .env with your actual backend URL
echo "VITE_API_URL=https://your-backend.onrender.com" > frontend/.env

# 2. Commit and push
git add frontend/.env
git commit -m "Fix: Update backend URL for Render deployment"
git push

# 3. Trigger manual deploy in Render dashboard
```

## ✅ Verification Checklist

- [ ] Backend URL is correct in frontend/.env
- [ ] Environment variables set in Render frontend service
- [ ] Backend CORS configured with correct frontend URL
- [ ] Both services deployed successfully
- [ ] Browser console shows correct API URL in debug info
- [ ] Registration/login works without localhost errors

## 🆘 If Still Not Working

1. **Check Network Tab**: See what URL the request is actually going to
2. **Check Backend Logs**: Look for incoming requests and CORS errors
3. **Test Backend Directly**: Visit backend URL in browser
4. **Clear Everything**: Clear browser cache, localStorage, and redeploy both services
