import express from "express";
import morgan from "morgan";
import connect from "./db/db..js";
import userRouter from "./routes/user.routes.js";
import cookieParser from "cookie-parser";
import cors from "cors";
import projectRouter from "./routes/project.routes.js";
import aiRouter from "./routes/ai.routes.js";

connect();

const app = express();

app.use(morgan("dev"));
app.use(express.json());
app.use(express.urlencoded({extended: true}));
app.use(cookieParser());
// CORS configuration using environment variables
const allowedOrigins = [
    process.env.FRONTEND_URL,
    process.env.LOCAL_FRONTEND_URL
].filter(Boolean); // Remove any undefined values

app.use(cors({
    origin: allowedOrigins,
    methods: ["GET", "POST"],
    allowedHeaders: ["my-custom-header"],
    credentials: true
}));

app.get("/",(req,res)=>{
    res.send("Hello World");
})

app.use("/users",userRouter);
app.use("/projects",projectRouter);
app.use("/ai",aiRouter);

export default app;