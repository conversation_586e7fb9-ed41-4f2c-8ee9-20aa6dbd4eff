import * as userService from "../services/user.service.js";
import { validationResult } from "express-validator";
import userModel from "../models/user.model.js";
import redisClient from "../services/redis.service.js";

export const createUserController = async(req,res)=>{

    const errors = validationResult(req);

    if(!errors.isEmpty()){
        return res.status(400).json({errors: errors.array()});
    }

    try{
        const user = await userService.createUser(req.body);
        const token = user.generateJWT();
        res.status(201).json({user,token});
    }
    catch(err){
        res.status(500).json({message: err.message});
    }
} 

export const loginController = async(req,res)=>{
    const errors = validationResult(req);

    if(!errors.isEmpty()){
        return res.status(400).json({errors: errors.array()});
    }

    try{
        const {email,password} = req.body;
        const user = await userModel.findOne({email}).select("+password");

        if(!user){
            return res.status(401).json({message: "Invalid credentials"});
        }

        const isMatch = await user.isValidPassword(password);

        if(!isMatch){
            return res.status(401).json({message: "Invalid credentials"});
        }

        const token = await user.generateJWT();
        res.status(200).json({user,token});
    }
    catch(err){
        res.status(500).json({message: err.message});
    }
}

export const profileController = async(req,res)=>{
    res.status(200).json({user: req.user});
}

export const logoutController = async(req,res)=>{
    try{
        const token = req.cookies.token || req.headers.authorization?.split(" ")[1];
        redisClient.set(token,'logout','EX',60*60*24);
        res.status(200).json({message: "Logged out successfully"});
    }
    catch(err){
        res.status(500).json({message: err.message});
    }
}

export const getAllUsers = async(req,res)=>{
    try{  
        const loggedInUser = await userModel.findOne({email: req.user.email});
        const allUsers = await userService.getAllUsers(loggedInUser._id);
        res.status(200).json({allUsers});
    }
    catch(err){
        res.status(500).json({message: err.message});
    }
}